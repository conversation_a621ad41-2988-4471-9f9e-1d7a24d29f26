import React from "react";
import { cn } from "@/libs/utils";
import { VariantSelectorProps } from "../types";
import { ExtendedVariant } from "@/types/Medusa/Product";
import { useProductContext } from "../context/ProductContext";

const VariantSelector: React.FC<VariantSelectorProps> = ({
  strapiProduct,
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const { activeVariant, setActiveVariant } = useProductContext();
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  const handleVariantChange = (variant: ExtendedVariant) => {
    setActiveVariant(variant);
  };

  const ProductVariants = (medusaProduct?.variants || [])
    .slice()
    .sort((a, b) => a.variant_rank - b.variant_rank);

  return (
    <div className="mb-3 flex items-center gap-4">
      {ProductVariants.map((variant) => (
        <div key={variant.id} className="relative flex-1">
          <button
            onClick={() => handleVariantChange(variant)}
            className={cn(
              "uppercase h-[35px] w-full rounded-[8px] text-[11.5px] font-obviously leading-[1.5px] cursor-pointer",
              activeVariant?.id === variant.id
                ? "text-white font-semibold"
                : "border-[1.5px]"
            )}
            style={
              activeVariant?.id === variant.id
                ? { backgroundColor: primaryColor }
                : { borderColor: primaryColor, color: primaryColor }
            }
          >
            {variant.title}
          </button>

          {/* Show best deal badge conditionally — you can change the logic here */}
          {true && (
            <span
              className="absolute -top-2.5 -left-2.5 z-10 w-[60px] h-[20px] bg-[url('https://dms.mydukaan.io/original/webp/media/5046cf71-d0aa-47ab-aa4a-a84aa818490c.png')] bg-no-repeat bg-contain bg-center"
              aria-hidden="true"
            />
          )}
        </div>
      ))}
    </div>
  );
};

export default VariantSelector;
