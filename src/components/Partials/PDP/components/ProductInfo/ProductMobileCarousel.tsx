import React, { useState, useEffect, useCallback } from "react";
import MobileProductCarousel from "../../MobileProductCarousel";
import { ProductMobileCarouselProps } from "../../types";
import { useProductContext } from "../../context/ProductContext";

/**
 * ProductMobileCarousel Component
 *
 * Wrapper for the mobile product carousel with props-based data
 */
export const ProductMobileCarousel: React.FC<ProductMobileCarouselProps> = ({
  strapiProduct,
  medusaProduct,
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Get combined images and active variant from context
  const {
    combinedImages,
    activeVariant,
    medusaProduct: contextMedusaProduct,
  } = useProductContext();

  // Function to find the first variant-specific image index
  const findFirstVariantImageIndex = useCallback(() => {
    if (!activeVariant?.variant_image || !contextMedusaProduct?.images) {
      return 0;
    }

    // Get the number of global images (they come first in combinedImages)
    const globalImageCount = contextMedusaProduct.images.length;

    // Find the first variant image with rank 0
    const firstVariantImage = activeVariant.variant_image
      .sort((a, b) => a.rank - b.rank)
      .find((img) => img.rank === 0);

    if (firstVariantImage) {
      // Return the index where variant images start (after global images)
      return globalImageCount;
    }

    return 0;
  }, [activeVariant?.variant_image, contextMedusaProduct?.images]);

  // Reset carousel to first variant image when activeVariant changes
  useEffect(() => {
    const targetIndex = findFirstVariantImageIndex();
    setCurrentSlide(targetIndex);
  }, [findFirstVariantImageIndex]); // Depend on the memoized function

  return (
    <MobileProductCarousel
      images={combinedImages}
      title={medusaProduct?.title || "Product"}
      currentSlide={currentSlide}
      onSlideChange={setCurrentSlide}
      primaryColor={strapiProduct?.primary_color || "#036A38"}
    />
  );
};
